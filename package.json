{"name": "startwrite-monorepo", "private": true, "version": "0.0.0", "type": "module", "workspaces": ["packages/*"], "scripts": {"dev": "concurrently npm:dev:client npm:dev:server", "dev:client": "vite", "dev:server": "tsx --watch --env-file packages/app/.env packages/app/src/hono-server.ts", "predev": "npx kill-port 4000 && npx kill-port 5173"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tanstack/router-devtools": "^1.131.26", "@tanstack/router-vite-plugin": "^1.131.26", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "pino-pretty": "^13.1.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "dependencies": {"@ai-sdk/google": "^2.0.7", "@ai-sdk/react": "^2.0.15", "@hono/node-server": "^1.19.0", "@tanstack/react-router": "^1.131.26", "ai": "^5.0.15", "hono": "^4.9.2", "pino": "^9.9.0"}}