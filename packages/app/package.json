{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ai-sdk/google": "^2.0.6", "@tiptap/extension-character-count": "^3.2.0", "@tiptap/extension-focus": "^3.2.0", "@tiptap/extension-placeholder": "^3.2.0", "@tiptap/pm": "^3.2.0", "@tiptap/react": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"tsx": "^4.20.4"}}