@tailwind base;
@tailwind components;
@tailwind utilities;

/* TipTap Editor Styles */
.ProseMirror {
  outline: none;
}

.ProseMirror.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

.ProseMirror.has-focus {
  outline: none;
}

/* Prose styling for editor content */
.prose {
  color: #374151;
  line-height: 1.75;
}

.prose p {
  margin-bottom: 1.25em;
}

.prose h1, .prose h2, .prose h3 {
  color: #111827;
  font-weight: 600;
  line-height: 1.25;
}

.prose h1 {
  font-size: 1.875rem;
  margin-bottom: 1rem;
}

.prose h2 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.prose ul, .prose ol {
  margin-bottom: 1.25em;
  padding-left: 1.5em;
}

.prose li {
  margin-bottom: 0.5em;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}

.prose em {
  font-style: italic;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
}

/* AI Block Styles */
.ai-text-wrapper {
  margin: 0.5rem 0;
}

.ai-prompt-wrapper {
  margin: 1rem 0;
}

.ai-generated-text {
  color: #2563eb;
  line-height: 1.75;
}

.ai-text-wrapper:hover .ai-indicator {
  opacity: 1;
}

.ai-indicator {
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* Smooth animations */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}