import { Hono } from 'hono';
import { logger } from 'hono/logger';
import { requestId } from 'hono/request-id';
import { streamText, convertToModelMessages, type UIMessage } from 'ai';
import { google } from '@ai-sdk/google';

const app = new Hono().basePath('/api');

app.use('*', requestId());
app.use('*', logger());



const model = google('gemini-2.5-pro');
app.post('/suggest', async (c) => {
  try {
    const { text } = await c.req.json();

const prompt = `
Help user keep writing; provide next prompt guides which could help user writing. Ask a direct question that prompts the user's next writing action based on their last text. Do not write new content; only ask or give a brief nudge. If no context is provided, ask what they are writing and their next goal. Always return one or two sentences;
the user's last text: ${text}
`

    const result = await streamText({
      // model: google('gemini-1.5-pro'),
      model,
      prompt,
      // `Based on the following text, suggest a next topic to continue the thought process:\n\n${text}`,
    });

    return result.toTextStreamResponse();
  } catch (error: unknown) {
    console.error('Error in /api/suggest:', error);
    c.status(500)
    return c.json({ error: 'Internal Server Error', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

app.post('/continue', async (c) => {
  try {
    const { text } = await c.req.json();

const prompt = `
Help user keep writing; provide next prompt guides which could help user writing. Ask a direct question that prompts the user's next writing action based on their last text. Do not write new content; only ask or give a brief nudge. If no context is provided, ask what they are writing and their next goal. Always return one or two sentences;
the user's last text: ${text}
`

    const result = await streamText({
      model,
      // model: google('gemini-1.5-pro'),
      prompt,
      // prompt: `Continue writing based on the following text. Provide a few sentences to continue the thought process:\n\n${text}`,
    });

    return result.toTextStreamResponse();
  } catch (error: unknown) {
    console.error('Error in /api/continue:', error);
    c.status(500)
    return c.json({ error: 'Internal Server Error', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

app.post('/chat', async (c) => {
  try {
    const { messages }: { messages: Array<Omit<UIMessage, 'id'>> } = await c.req.json();

    const result = await streamText({
      model,
      // model: google('gemini-1.5-pro'),
      messages: convertToModelMessages(messages),
    });

    return result.toUIMessageStreamResponse();
  } catch (error: unknown) {
    console.error('Error in /api/chat:', error);
    c.status(500)
    return c.json({ error: 'Internal Server Error', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

export default app;
