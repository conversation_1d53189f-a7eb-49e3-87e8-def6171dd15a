import React from 'react';
import { RootRoute, Route, Router } from '@tanstack/react-router';
import App from './App';
import NotesView from './components/NotesView';
import ToolsView from './components/ToolsView';

const rootRoute = new RootRoute({
  component: App,
});


const notesRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/notes',
  component: NotesView,
});


const toolsRoute = new Route({
  getParentRoute: () => rootRoute,
  path: '/',
  component: ToolsView,
});

const routeTree = rootRoute.addChildren([notesRoute, toolsRoute]);

export const router = new Router({ routeTree });

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}
