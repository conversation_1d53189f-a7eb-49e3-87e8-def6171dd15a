import React, { useState } from 'react';
import { Outlet } from '@tanstack/react-router';
import { Navigation } from './components/Navigation';
import { TanStackRouterDevtools } from '@tanstack/router-devtools';
import AICollaborationPanel from './components/AICollaborationPanel';
import WritingToolsSidebar from './components/WritingToolsSidebar';

function App() {
  const [isAIPanelOpen, setIsAIPanelOpen] = useState(false);
  const [isWritingToolsOpen, setIsWritingToolsOpen] = useState(false);

  return (
    <>
      <div className="min-h-screen bg-slate-50 flex flex-col">
        <Navigation
          onToggleAI={() => setIsAIPanelOpen(!isAIPanelOpen)}
          onToggleWritingTools={() => setIsWritingToolsOpen(!isWritingToolsOpen)}
        />
        <div className="flex-1 flex">
          <main className="flex-1 relative">
            <Outlet />
          </main>
          
          {isAIPanelOpen && (
            <AICollaborationPanel onClose={() => setIsAIPanelOpen(false)} />
          )}
          
          {isWritingToolsOpen && (
            <WritingToolsSidebar onClose={() => setIsWritingToolsOpen(false)} />
          )}
        </div>
      </div>
      <TanStackRouterDevtools />
    </>
  );
}

export default App;
