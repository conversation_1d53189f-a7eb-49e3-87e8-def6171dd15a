import React, { useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Focus from '@tiptap/extension-focus';
import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer, NodeViewWrapper } from '@tiptap/react';
import { Sparkles, ArrowRight, Edit3, MessageCircle } from 'lucide-react';

// Custom AI Text Node - renders as blue text with subtle AI indicator
const AIText = Node.create({
  name: 'aiText',
  group: 'block',
  content: 'inline*',
  
  addAttributes() {
    return {
      isAI: {
        default: true,
      },
      suggestionId: {
        default: null,
        renderHTML: attributes => {
          if (!attributes.suggestionId) {
            return {};
          }
          return {
            'data-suggestion-id': attributes.suggestionId,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="ai-text"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 
      'data-type': 'ai-text',
      'class': 'ai-generated-text'
    }), 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(AITextComponent);
  },
});

function AITextComponent({ node, updateAttributes, deleteNode, editor }: any) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <NodeViewWrapper 
      className="ai-text-wrapper"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative group">
        {/* Subtle AI indicator - only shows on hover */}
        {isHovered && (
          <div className="absolute -left-8 top-0 opacity-60">
            <Sparkles className="text-blue-400" size={14} />
          </div>
        )}
        
        {/* AI generated text with blue styling */}
        <div 
          className="text-blue-600 leading-relaxed cursor-text"
          contentEditable={false}
        >
          {node.textContent}
        </div>
      </div>
    </NodeViewWrapper>
  );
}

// Custom AI Prompt Node - renders as subtle prompt that can be responded to
const AIPrompt = Node.create({
  name: 'aiPrompt',
  group: 'block',
  content: 'inline*',
  
  addAttributes() {
    return {
      isActive: {
        default: false,
      },
      hasResponse: {
        default: false,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="ai-prompt"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'ai-prompt' }), 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(AIPromptComponent);
  },
});

function AIPromptComponent({ node, updateAttributes, deleteNode, editor }: any) {
  const [isExpanded, setIsExpanded] = useState(node.attrs.isActive);
  const [userResponse, setUserResponse] = useState('');

  const handleExpand = () => {
    setIsExpanded(true);
    updateAttributes({ isActive: true });
  };

  const handleAddResponse = () => {
    if (userResponse.trim()) {
      // Find the position after this node
      const pos = editor.view.state.selection.from;
      
      // Insert user response as regular paragraph
      editor.chain()
        .focus()
        .insertContentAt(pos, `<p>${userResponse}</p>`)
        .run();
      
      setUserResponse('');
      setIsExpanded(false);
      updateAttributes({ isActive: false, hasResponse: true });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddResponse();
    }
  };

  return (
    <NodeViewWrapper className="ai-prompt-wrapper">
      <div className="my-4">
        {/* AI Prompt - styled like blue text with left border */}
        <div className="border-l-2 border-blue-400 pl-4 mb-3">
          <div className="text-blue-600 leading-relaxed cursor-pointer" onClick={handleExpand}>
            {node.textContent}
          </div>
        </div>

        {/* Expandable response area */}
        {isExpanded && (
          <div className="ml-6 space-y-3 animate-fade-in">
            <textarea
              value={userResponse}
              onChange={(e) => setUserResponse(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Write here..."
              className="w-full p-3 border-l-2 border-gray-200 pl-4 bg-transparent resize-none focus:outline-none focus:border-blue-400 transition-colors duration-200"
              rows={3}
              autoFocus
            />
            <div className="flex items-center space-x-3 ml-4">
              <button
                onClick={handleAddResponse}
                disabled={!userResponse.trim()}
                className="text-sm text-blue-600 hover:text-blue-700 disabled:text-gray-400 transition-colors duration-200"
              >
                Continue writing
              </button>
              <button
                onClick={() => setIsExpanded(false)}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                Skip
              </button>
            </div>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
}

interface EnhancedTipTapEditorProps {
  initialContent?: string;
  aiPrompts?: string[];
  placeholder?: string;
  onContentChange?: (content: string) => void;
}

export default function EnhancedTipTapEditor({ 
  initialContent = '', 
  aiPrompts = [],
  placeholder = "Start writing...",
  onContentChange 
}: EnhancedTipTapEditorProps) {
  const [lastWordCount, setLastWordCount] = useState(0);

  const handleSuggestTopic = async () => {
    if (!editor) return;

    const suggestionId = `suggestion-${Date.now()}`;
    const selection = editor.state.selection;
    const insertPos = selection.to;

    // Insert a placeholder AIText node
    editor.chain().focus(insertPos).insertContent({
      type: 'aiText',
      attrs: { suggestionId },
      content: [{ type: 'text', text: '...' }],
    }).run();

    try {
      const response = await fetch('/api/suggest', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: editor.getHTML() }),
      });

      if (!response.ok || !response.body) {
        throw new Error('Network response was not ok.');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let suggestionText = '';
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        suggestionText += decoder.decode(value);
      }
      
      // Find the placeholder node and update it
      let nodeToUpdate = null;
      let nodePos = -1;
      editor.state.doc.descendants((node, pos) => {
        if (node.type.name === 'aiText' && node.attrs.suggestionId === suggestionId) {
          nodeToUpdate = node;
          nodePos = pos;
          return false;
        }
      });
      
      if (nodeToUpdate && nodePos !== -1) {
        const tr = editor.state.tr;
        const from = nodePos + 1;
        const to = from + nodeToUpdate.content.size;
        tr.replaceWith(from, to, editor.state.schema.text(suggestionText));
        editor.view.dispatch(tr);
      } else {
         // Fallback if node not found: just insert the text
         editor.chain().focus().insertContentAt(insertPos, suggestionText).run();
      }

    } catch (error) {
      console.error("Failed to fetch suggestion:", error);
      // Here I should probably remove the placeholder
       let nodeToUpdate = null;
      let nodePos = -1;
      editor.state.doc.descendants((node, pos) => {
        if (node.type.name === 'aiText' && node.attrs.suggestionId === suggestionId) {
          nodeToUpdate = node;
          nodePos = pos;
          return false;
        }
      });
       if (nodeToUpdate && nodePos !== -1) {
         editor.chain().focus().deleteRange({ from: nodePos, to: nodePos + nodeToUpdate.nodeSize }).run();
       }
    }
  };

  const editor = useEditor({
    extensions: [
      StarterKit,
      AIText,
      AIPrompt,
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount,
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      onContentChange?.(content);
      
      const wordCount = editor.storage.characterCount.words();
      
      // Automatically insert AI prompts as user writes
      if (wordCount > lastWordCount && wordCount > 15 && wordCount % 20 === 0) {
        setTimeout(() => {
          if (aiPrompts.length > 0) {
            insertAIContent();
          }
        }, 1500);
      }
      
      setLastWordCount(wordCount);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-8 leading-relaxed',
      },
      handleKeyDown: (view, event) => {
        if (event.shiftKey && event.key === 'Enter') {
          event.preventDefault();
          handleSuggestTopic();
          return true;
        }
        return false;
      },
    },
  });

  const insertAIContent = () => {
    if (!editor || aiPrompts.length === 0) return;
    
    const randomPrompt = aiPrompts[Math.floor(Math.random() * aiPrompts.length)];
    
    // Sometimes insert AI text, sometimes insert AI prompt
    const shouldInsertPrompt = Math.random() > 0.6;
    
    if (shouldInsertPrompt) {
      editor.chain()
        .focus()
        .insertContent({
          type: 'aiPrompt',
          content: [{ type: 'text', text: randomPrompt }],
          attrs: { isActive: false, hasResponse: false }
        })
        .run();
    } else {
      // Insert AI-generated text that continues the thought
      const aiContinuations = [
        "This connects to a deeper pattern in how we process experiences.",
        "The emotional undertone here suggests something worth exploring further.",
        "There's an interesting tension between what's said and what's felt.",
        "This moment reveals something important about your perspective.",
        "The details you've chosen to include tell their own story."
      ];
      
      const aiText = aiContinuations[Math.floor(Math.random() * aiContinuations.length)];
      
      editor.chain()
        .focus()
        .insertContent({
          type: 'aiText',
          content: [{ type: 'text', text: aiText }],
          attrs: { isAI: true }
        })
        .run();
    }
  };

  const handleContinueWriting = async () => {
    if (!editor) return;

    const continuationId = `continuation-${Date.now()}`;
    const selection = editor.state.selection;
    const insertPos = selection.to;

    // Insert a placeholder AIText node
    editor.chain().focus(insertPos).insertContent({
      type: 'aiText',
      attrs: { suggestionId: continuationId }, // Re-using suggestionId attr for tracking
      content: [{ type: 'text', text: '...' }],
    }).run();

    try {
      const response = await fetch('/api/continue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: editor.getHTML() }),
      });

      if (!response.ok || !response.body) {
        throw new Error('Network response was not ok.');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let continuationText = '';
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        continuationText += decoder.decode(value);
      }
      
      // Find the placeholder node and update it
      let nodeToUpdate = null;
      let nodePos = -1;
      editor.state.doc.descendants((node, pos) => {
        if (node.type.name === 'aiText' && node.attrs.suggestionId === continuationId) {
          nodeToUpdate = node;
          nodePos = pos;
          return false;
        }
      });
      
      if (nodeToUpdate && nodePos !== -1) {
        const tr = editor.state.tr;
        const from = nodePos + 1;
        const to = from + nodeToUpdate.content.size;
        tr.replaceWith(from, to, editor.state.schema.text(continuationText));
        editor.view.dispatch(tr);
      } else {
         // Fallback if node not found: just insert the text
         editor.chain().focus().insertContentAt(insertPos, continuationText).run();
      }

    } catch (error) {
      console.error("Failed to fetch continuation:", error);
      // Here I should probably remove the placeholder
       let nodeToUpdate = null;
      let nodePos = -1;
      editor.state.doc.descendants((node, pos) => {
        if (node.type.name === 'aiText' && node.attrs.suggestionId === continuationId) {
          nodeToUpdate = node;
          nodePos = pos;
          return false;
        }
      });
       if (nodeToUpdate && nodePos !== -1) {
         editor.chain().focus().deleteRange({ from: nodePos, to: nodePos + nodeToUpdate.nodeSize }).run();
       }
    }
  };

  const handleManualAIPrompt = () => {
    insertAIContent();
  };

  const wordCount = editor?.storage.characterCount.words() || 0;
  const charCount = editor?.storage.characterCount.characters() || 0;

  return (
    <div className="relative">
      <div className="bg-white rounded-2xl border border-slate-200 shadow-sm overflow-hidden">
        <EditorContent 
          editor={editor} 
          className="min-h-[500px] focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-all duration-200"
        />
        
        {/* Minimal Footer */}
        <div className="flex items-center justify-between px-8 py-4 bg-slate-50 border-t border-slate-200">
          <div className="flex items-center space-x-6 text-sm text-slate-500">
            <span>{wordCount} words</span>
            <span>{charCount} characters</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleContinueWriting}
              className="flex items-center space-x-2 px-3 py-1.5 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200 text-sm"
            >
              <MessageCircle size={14} />
              <span>Continue with AI</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
