import React, { useState } from 'react';
import { Plus, Search, Tag, Calendar, Star } from 'lucide-react';
import { TipTapEditor } from './TipTapEditor';

interface Note {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
  isStarred: boolean;
}

export default function NotesView() {
  const [notes] = useState<Note[]>([
    {
      id: '1',
      title: 'Meeting Ideas',
      content: 'Key insights from today\'s brainstorming session about user experience improvements...',
      tags: ['work', 'ideas'],
      createdAt: new Date(2024, 0, 15),
      isStarred: true
    },
    {
      id: '2',
      title: 'Book Notes: Deep Work',
      content: 'The ability to focus without distraction on a cognitively demanding task...',
      tags: ['reading', 'productivity'],
      createdAt: new Date(2024, 0, 14),
      isStarred: false
    },
    {
      id: '3',
      title: 'Project Roadmap',
      content: 'Q1 objectives and key milestones for the new product launch...',
      tags: ['planning', 'work'],
      createdAt: new Date(2024, 0, 13),
      isStarred: true
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isCreatingNote, setIsCreatingNote] = useState(false);

  const handleCreateNote = () => {
    setIsCreatingNote(true);
    setSelectedNote(null);
  };

  const handleSelectNote = (note: Note) => {
    setSelectedNote(note);
    setIsCreatingNote(false);
  };

  const handleBackToList = () => {
    setSelectedNote(null);
    setIsCreatingNote(false);
  };
  return (
    <div className="max-w-6xl mx-auto p-6">
      {selectedNote || isCreatingNote ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <button
              onClick={handleBackToList}
              className="text-slate-600 hover:text-slate-900 transition-colors duration-200"
            >
              ← Back to Notes
            </button>
            
            {selectedNote && (
              <div className="flex items-center space-x-2">
                {selectedNote.isStarred && (
                  <Star className="text-amber-400 fill-current" size={16} />
                )}
                <span className="text-sm text-slate-500">
                  {selectedNote.createdAt.toLocaleDateString()}
                </span>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <input
              type="text"
              placeholder="Note title..."
              defaultValue={selectedNote?.title || ''}
              className="w-full text-2xl font-semibold text-slate-900 bg-transparent border-none focus:outline-none placeholder-slate-400"
            />
            
            <TipTapEditor
              placeholder="Start writing your note..."
              initialContent={selectedNote?.content || ''}
              showAIPrompts={true}
            />
          </div>
        </div>
      ) : (
        <>
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-semibold text-slate-900 mb-2">Your Notes</h1>
                <p className="text-slate-600">Capture and organize your thoughts</p>
              </div>
              
              <button 
                onClick={handleCreateNote}
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200"
              >
                <Plus size={18} />
                <span>New Note</span>
              </button>
            </div>

            <div className="mt-6 relative">
              <Search className="absolute left-3 top-3 text-slate-400" size={18} />
              <input
                type="text"
                placeholder="Search notes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>
          </div>
              
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {notes
              .filter(note => note.title.toLowerCase().includes(searchQuery.toLowerCase()))
              .map((note) => (
              <div
                key={note.id}
                onClick={() => handleSelectNote(note)}
                className="bg-white border border-slate-200 rounded-xl p-6 hover:shadow-lg hover:border-slate-300 transition-all duration-200 cursor-pointer group"
              >
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-slate-900 group-hover:text-indigo-700 transition-colors duration-200">
                    {note.title}
                  </h3>
                  {note.isStarred && (
                    <Star className="text-amber-400 fill-current" size={16} />
                  )}
                </div>
                
                <p className="text-slate-600 text-sm line-clamp-3 mb-4">
                  {note.content}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {note.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center space-x-1 px-2 py-1 bg-slate-100 text-slate-700 rounded-full text-xs"
                      >
                        <Tag size={10} />
                        <span>{tag}</span>
                      </span>
                    ))}
                  </div>

                  <div className="flex items-center space-x-1 text-xs text-slate-500">
                    <Calendar size={12} />
                    <span>{note.createdAt.toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            ))
            }
            {notes.filter(note => note.title.toLowerCase().includes(searchQuery.toLowerCase())).length === 0 && (
              <p className="text-slate-500">No matching notes found.</p>
            )}
          </div>
        </>
      )}
    </div>
  );
}