import React from 'react';
import { X, Type, Palette, AlignLeft, List, Bold, Italic, Underline } from 'lucide-react';

interface WritingToolsSidebarProps {
  onClose: () => void;
}

export default function WritingToolsSidebar({ onClose }: WritingToolsSidebarProps) {
  const formattingTools = [
    { icon: Bold, label: 'Bold', shortcut: '⌘B' },
    { icon: Italic, label: 'Italic', shortcut: '⌘I' },
    { icon: Underline, label: 'Underline', shortcut: '⌘U' },
  ];

  const layoutTools = [
    { icon: AlignLeft, label: 'Align Text' },
    { icon: List, label: 'Lists' },
    { icon: Type, label: 'Typography' },
  ];

  return (
    <div className="w-80 bg-white border-l border-slate-200 shadow-lg flex flex-col">
      <div className="flex items-center justify-between p-4 border-b border-slate-200">
        <h2 className="font-semibold text-slate-900">Writing Tools</h2>
        <button
          onClick={onClose}
          className="p-1 rounded-md text-slate-500 hover:text-slate-700 hover:bg-slate-100"
        >
          <X size={18} />
        </button>
      </div>

      <div className="flex-1 p-4 space-y-6">
        <div>
          <h3 className="text-sm font-medium text-slate-700 mb-3">Format Text</h3>
          <div className="grid grid-cols-3 gap-2">
            {formattingTools.map((tool, index) => {
              const Icon = tool.icon;
              return (
                <button
                  key={index}
                  className="flex flex-col items-center p-3 rounded-lg border border-slate-200 hover:border-slate-300 hover:bg-slate-50 transition-all duration-200"
                  title={`${tool.label} ${tool.shortcut || ''}`}
                >
                  <Icon size={18} className="text-slate-600 mb-1" />
                  <span className="text-xs text-slate-600">{tool.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-slate-700 mb-3">Layout</h3>
          <div className="space-y-2">
            {layoutTools.map((tool, index) => {
              const Icon = tool.icon;
              return (
                <button
                  key={index}
                  className="w-full flex items-center space-x-3 p-3 rounded-lg border border-slate-200 hover:border-slate-300 hover:bg-slate-50 transition-all duration-200 text-left"
                >
                  <Icon size={18} className="text-slate-600" />
                  <span className="text-sm text-slate-700">{tool.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-slate-700 mb-3">Themes</h3>
          <div className="grid grid-cols-4 gap-2">
            {['bg-slate-100', 'bg-blue-100', 'bg-green-100', 'bg-amber-100'].map((color, index) => (
              <button
                key={index}
                className={`h-12 rounded-lg ${color} border-2 border-transparent hover:border-slate-400 transition-all duration-200`}
              />
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-slate-700 mb-3">Focus Mode</h3>
          <button className="w-full p-3 bg-indigo-50 text-indigo-700 rounded-lg hover:bg-indigo-100 transition-colors duration-200">
            Enable Distraction-Free Writing
          </button>
        </div>
      </div>
    </div>
  );
}