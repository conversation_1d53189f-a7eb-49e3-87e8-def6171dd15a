import React, { useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Focus from '@tiptap/extension-focus';
import { <PERSON>rkles, ArrowRight, Lightbulb } from 'lucide-react';

interface TipTapEditorProps {
  placeholder?: string;
  onContentChange?: (content: string) => void;
  initialContent?: string;
  showAIPrompts?: boolean;
}

export function TipTapEditor({ 
  placeholder = "Start writing...", 
  onContentChange,
  initialContent = "",
  showAIPrompts = true 
}: TipTapEditorProps) {
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount,
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      onContentChange?.(content);
      
      if (showAIPrompts && content.length > 50 && editor.storage.characterCount) {
        const wordCount = editor.storage.characterCount.words();
        if (wordCount > 10 && wordCount % 25 === 0) {
          generateAISuggestions(content);
        }
      }
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[200px] p-6',
      },
    },
  });

  useEffect(() => {
    if (editor && initialContent !== editor.getHTML()) {
      editor.commands.setContent(initialContent);
    }
  }, [initialContent, editor]);

  const generateAISuggestions = async (content: string) => {
    setIsGenerating(true);
    
    const suggestions = [
      "What specific examples could illustrate this point?",
      "How does this connect to your broader goals?",
      "What questions does this raise for you?",
      "Can you expand on the emotions or feelings involved?",
      "What would someone who disagrees say about this?"
    ];
    
    setTimeout(() => {
      setAiSuggestions(suggestions.slice(0, 3));
      setShowAIPanel(true);
      setIsGenerating(false);
    }, 1000);
  };

  const insertAIPrompt = (prompt: string) => {
    if (editor) {
      editor.chain().focus().insertContent(`\n\n${prompt}\n\n`).run();
      setShowAIPanel(false);
    }
  };

  const wordCount = editor?.storage.characterCount?.words() || 0;
  const charCount = editor?.storage.characterCount?.characters() || 0;

  return (
    <div className="relative">
      <div className="bg-white rounded-2xl border border-slate-200 shadow-sm overflow-hidden">
        <EditorContent 
          editor={editor} 
          className="min-h-[300px] focus-within:ring-2 focus-within:ring-indigo-500 focus-within:border-transparent transition-all duration-200"
        />
        
        <div className="flex items-center justify-between px-6 py-3 bg-slate-50 border-t border-slate-200">
          <div className="flex items-center space-x-4 text-sm text-slate-500">
            <span>{wordCount} words</span>
            <span>{charCount} characters</span>
          </div>
          
          {showAIPrompts && (
            <button
              onClick={() => generateAISuggestions(editor?.getHTML() || "")}
              disabled={isGenerating || !editor}
              className="flex items-center space-x-2 px-3 py-1.5 bg-indigo-100 text-indigo-700 rounded-lg hover:bg-indigo-200 transition-colors duration-200 disabled:opacity-50"
            >
              <Sparkles size={14} />
              <span className="text-sm">
                {isGenerating ? "Thinking..." : "Get AI Help"}
              </span>
            </button>
          )}
        </div>
      </div>

      {showAIPanel && (
        <div className="absolute top-full left-0 right-0 mt-4 bg-white rounded-xl border border-slate-200 shadow-lg z-10 p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Lightbulb className="text-amber-500" size={16} />
            <h3 className="font-medium text-slate-900">AI suggests exploring:</h3>
          </div>
          
          <div className="space-y-2">
            {aiSuggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => insertAIPrompt(suggestion)}
                className="w-full text-left p-3 rounded-lg border border-slate-200 hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 group"
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-700 group-hover:text-indigo-700">
                    {suggestion}
                  </span>
                  <ArrowRight className="text-slate-400 group-hover:text-indigo-500" size={14} />
                </div>
              </button>
            ))}
          </div>
          
          <button
            onClick={() => setShowAIPanel(false)}
            className="mt-3 text-sm text-slate-500 hover:text-slate-700"
          >
            Dismiss suggestions
          </button>
        </div>
      )}
    </div>
  );
}
