import React, { useState } from 'react';
import EnhancedTipTapEditor from './EnhancedTipTapEditor';
import { <PERSON><PERSON><PERSON>, Zap, Brain, Edit3 } from 'lucide-react';

export default function ToolsView() {
  const [activeTemplate, setActiveTemplate] = useState<string | null>(null);

  const templates = [
    {
      id: 'brainstorm',
      title: 'Brainstorming Session',
      icon: Brain,
      description: 'Generate and explore ideas with AI assistance',
      initialContent: '<h2>Brainstorming Session</h2><p>What topic would you like to explore today?</p>',
      aiPrompts: [
        'What specific aspects of this feel most important to explore?',
        'Can you think of any examples that illustrate this?',
        'What questions does this raise for you?',
        'How might this connect to your broader goals?',
        'What would someone who disagrees say about this?'
      ]
    },
    {
      id: 'reflection',
      title: 'Deep Reflection',
      icon: Sparkles,
      description: 'Thoughtful analysis with guided questions',
      initialContent: '<h2>Reflection Space</h2><p>What experience or idea would you like to reflect on?</p>',
      aiPrompts: [
        'What emotions or feelings does this bring up for you?',
        'What patterns do you notice in this experience?',
        'How has your perspective on this evolved?',
        'What would you want to remember about this moment?',
        'What does this reveal about what matters to you?'
      ]
    },
    {
      id: 'creative',
      title: 'Creative Writing',
      icon: Edit3,
      description: 'Unleash creativity with AI collaboration',
      initialContent: '<h2>Creative Space</h2><p>Let your imagination flow...</p>',
      aiPrompts: [
        'What if you took this story in an unexpected direction?',
        'Can you add more sensory details to bring this to life?',
        'What\'s the underlying emotion driving this scene?',
        'How can you make this moment more vivid for the reader?',
        'What would happen if you changed the perspective?'
      ]
    },
    {
      id: 'freeform',
      title: 'Freeform Canvas',
      icon: Zap,
      description: 'Open canvas for any type of writing',
      initialContent: '<p>Start writing anything that comes to mind...</p>',
      aiPrompts: [
        'Tell me more about what you\'re thinking here.',
        'What led you to this particular thought?',
        'Can you expand on this idea with more detail?',
        'What examples or experiences come to mind?',
        'How does this connect to other things you\'ve been thinking about?'
      ]
    }
  ];

  const handleTemplateSelect = (template: typeof templates[0]) => {
    setActiveTemplate(template.id);
  };

  const selectedTemplate = templates.find(t => t.id === activeTemplate);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50/30">
      {!activeTemplate ? (
        <div className="max-w-4xl mx-auto p-8">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-light text-slate-800 mb-4">Writing Tools</h1>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Choose a template to start writing with AI assistance, or create your own canvas
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {templates.map((template) => {
              const Icon = template.icon;
              return (
                <button
                  key={template.id}
                  onClick={() => handleTemplateSelect(template)}
                  className="group p-8 bg-white rounded-2xl border border-slate-200 hover:border-indigo-300 hover:shadow-lg transition-all duration-300 text-left"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="p-3 bg-indigo-100 rounded-xl group-hover:bg-indigo-200 transition-colors duration-200">
                      <Icon className="text-indigo-600" size={24} />
                    </div>
                    <h3 className="text-xl font-semibold text-slate-800 group-hover:text-indigo-700 transition-colors duration-200">
                      {template.title}
                    </h3>
                  </div>
                  <p className="text-slate-600 leading-relaxed">
                    {template.description}
                  </p>
                </button>
              );
            })}
          </div>
        </div>
      ) : (
        <div className="max-w-4xl mx-auto p-8">
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => setActiveTemplate(null)}
              className="text-slate-600 hover:text-slate-800 transition-colors duration-200"
            >
              ← Back to Templates
            </button>
            <div className="flex items-center space-x-2 text-slate-600">
              {selectedTemplate && (() => {
                const SelectedIcon = selectedTemplate.icon;
                return (
                  <>
                    <SelectedIcon size={18} />
                    <span className="font-medium">{selectedTemplate.title}</span>
                  </>
                );
              })()}
            </div>
          </div>

          {selectedTemplate && (
            <EnhancedTipTapEditor
              initialContent={selectedTemplate.initialContent}
              aiPrompts={selectedTemplate.aiPrompts}
              placeholder="Continue writing..."
            />
          )}
        </div>
      )}
    </div>
  );
}
